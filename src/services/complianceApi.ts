
// API service for DBOS compliance endpoints
const API_BASE_URL = ''; //'http://localhost:3000';

// Types and Interfaces
export interface ComplianceDocument {
  id: string;
  content: string;
  documentType: 'contract' | 'policy' | 'procedure' | 'financial_report';
  uploadedAt: Date;
  status: 'pending' | 'processing' | 'compliant' | 'non_compliant' | 'requires_review';
}

export interface ComplianceRule {
  id: string;
  standard: 'SEC' | 'GLBA' | 'SOX' | 'GDPR' | 'CCPA';
  ruleType: 'data_protection' | 'financial_disclosure' | 'privacy' | 'security';
  description: string;
  pattern: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
}

export interface KYCProfile {
  customerId: string;
  personalInfo: {
    name: string;
    dateOfBirth: string;
    ssn: string;
    address: string;
  };
  riskScore: number;
  status: 'pending' | 'approved' | 'rejected' | 'under_review';
  lastUpdated: Date;
}

export interface ComplianceViolation {
  documentId: string;
  ruleId: string;
  violationType: string;
  description: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  recommendedAction: string;
  detectedAt: Date;
}

export interface ComplianceReport {
  id: string;
  reportType: 'monthly' | 'quarterly' | 'annual' | 'incident';
  generatedAt: Date;
  compliance_rate: number;
  violations: ComplianceViolation[];
  recommendations: string[];
}

export interface RegulatoryUpdate {
  id: string;
  standard: string;
  title: string;
  description: string;
  effectiveDate: Date;
  impact: 'low' | 'medium' | 'high';
  actionRequired: boolean;
}

// Workflow result types
export interface DocumentProcessingResult {
  documentId: string;
  status: 'compliant' | 'non_compliant' | 'requires_review';
  violations: ComplianceViolation[];
  complianceScore: number;
}

export interface KYCProcessingResult {
  customerId: string;
  status: 'approved' | 'rejected' | 'under_review';
  riskScore: number;
  reasons: string[];
}

export interface ReportGenerationResult {
  reportId: string;
  reportType: ComplianceReport['reportType'];
  generatedAt: Date;
  downloadUrl?: string;
}

export type WorkflowResult = DocumentProcessingResult | KYCProcessingResult | ReportGenerationResult;

export interface KYCAdditionalInfo {
  employmentStatus?: 'employed' | 'unemployed' | 'self_employed' | 'retired';
  annualIncome?: number;
  sourceOfFunds?: string;
  politicallyExposed?: boolean;
  businessPurpose?: string;
  referralSource?: string;
  documents?: {
    idDocument?: string;
    proofOfAddress?: string;
    financialStatements?: string[];
  };
}

export interface WorkflowStatus {
  workflowId: string;
  status: 'pending' | 'running' | 'completed' | 'failed';
  workflowName: string;
  events: Record<string, unknown>;
  currentStep?: string;
  progress?: number;
  estimatedCompletion?: string;
}

// Dashboard Data Interfaces
export interface DashboardMetrics {
  complianceRate: number;
  activeViolations: number;
  pendingKYC: number;
  completedReports: number;
  regulatoryUpdates: number;
}

export interface ComplianceStandard {
  name: string;
  compliance: number;
  violations: number;
  lastCheck: string;
  status: 'compliant' | 'issues';
}

export interface RecentViolation {
  id: number;
  document: string;
  violation: string;
  severity: 'Critical' | 'High' | 'Medium' | 'Low';
  date: string;
  status: string;
}

export interface AIInsight {
  type: 'pattern' | 'improvement' | 'risk';
  title: string;
  description: string;
  color: 'blue' | 'green' | 'orange' | 'red';
}

export interface WorkflowPerformance {
  documentProcessing: string;
  kycCompletionRate: string;
  zeroDowntime: string;
  costSavings: string;
}

// API Functions
export const complianceApi = {
  // Submit document for compliance check
  async submitDocument(documentData: {
    content: string;
    documentType: ComplianceDocument['documentType'];
    fileName?: string;
  }): Promise<{ workflowId: string; document: ComplianceDocument }> {
    const response = await fetch(`${API_BASE_URL}/api/compliance/document`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(documentData),
    });
    
    if (!response.ok) {
      throw new Error('Failed to submit document for compliance check');
    }
    
    return response.json();
  },

  // Submit customer for KYC processing
  async submitKYC(customerData: {
    personalInfo: KYCProfile['personalInfo'];
    additionalInfo?: KYCAdditionalInfo;
  }): Promise<{ workflowId: string; profile: KYCProfile }> {
    const response = await fetch(`${API_BASE_URL}/api/kyc/customer`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(customerData),
    });
    
    if (!response.ok) {
      throw new Error('Failed to submit customer for KYC processing');
    }
    
    return response.json();
  },

  // Generate compliance reports
  async generateReport(reportConfig: {
    reportType: ComplianceReport['reportType'];
    dateRange?: {
      startDate: string;
      endDate: string;
    };
    standards?: string[];
  }): Promise<{ workflowId: string; report: ComplianceReport }> {
    const response = await fetch(`${API_BASE_URL}/api/reports/generate`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(reportConfig),
    });
    
    if (!response.ok) {
      throw new Error('Failed to generate compliance report');
    }
    
    return response.json();
  },

  // Get workflow progress
  async getWorkflowStatus(workflowId: string): Promise<WorkflowStatus> {
    const response = await fetch(`${API_BASE_URL}/api/workflows/${workflowId}/status`);

    if (!response.ok) {
      throw new Error('Failed to get workflow status');
    }

    return response.json();
  },

  // Get workflow results
  async getWorkflowResult(workflowId: string): Promise<WorkflowResult> {
    const response = await fetch(`${API_BASE_URL}/api/workflows/${workflowId}/result`);

    if (!response.ok) {
      throw new Error('Failed to get workflow result');
    }

    return response.json();
  },

  // Dashboard methods
  async getDashboardMetrics(): Promise<DashboardMetrics> {
    const response = await fetch(`${API_BASE_URL}/api/dashboard/metrics`);

    if (!response.ok) {
      throw new Error('Failed to get dashboard metrics');
    }

    return response.json();
  },

  // Compliance overview methods
  async getRecentViolations(): Promise<RecentViolation[]> {
    const response = await fetch(`${API_BASE_URL}/api/compliance/violations/recent`);

    if (!response.ok) {
      throw new Error('Failed to get recent violations');
    }

    return response.json();
  },

  async getComplianceStandards(): Promise<ComplianceStandard[]> {
    const response = await fetch(`${API_BASE_URL}/api/compliance/standards`);

    if (!response.ok) {
      throw new Error('Failed to get compliance standards');
    }

    return response.json();
  },

  async getAIInsights(): Promise<AIInsight[]> {
    const response = await fetch(`${API_BASE_URL}/api/dashboard/ai-insights`);

    if (!response.ok) {
      throw new Error('Failed to get AI insights');
    }

    return response.json();
  },

  async getWorkflowPerformance(): Promise<WorkflowPerformance> {
    const response = await fetch(`${API_BASE_URL}/api/dashboard/workflow-performance`);

    if (!response.ok) {
      throw new Error('Failed to get workflow performance');
    }

    return response.json();
  },

  // Document methods
  async getRecentDocuments(): Promise<ComplianceDocument[]> {
    const response = await fetch(`${API_BASE_URL}/api/documents/recent`);

    if (!response.ok) {
      throw new Error('Failed to get recent documents');
    }

    return response.json();
  },

  async getDocumentStats(): Promise<{
    documentsScanned: number;
    violationsDetected: number;
    avgProcessingTime: string;
    violationRate: number;
  }> {
    const response = await fetch(`${API_BASE_URL}/api/documents/stats`);

    if (!response.ok) {
      throw new Error('Failed to get document stats');
    }

    return response.json();
  },

  async submitDocumentFile(file: File, documentType: ComplianceDocument['documentType']): Promise<{ workflowId: string; document: ComplianceDocument }> {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('documentType', documentType);

    const response = await fetch(`${API_BASE_URL}/api/compliance/document/upload`, {
      method: 'POST',
      body: formData,
    });

    if (!response.ok) {
      throw new Error('Failed to upload document file');
    }

    return response.json();
  },

  // KYC methods
  async getKYCQueue(): Promise<KYCProfile[]> {
    const response = await fetch(`${API_BASE_URL}/api/kyc/queue`);

    if (!response.ok) {
      throw new Error('Failed to get KYC queue');
    }

    return response.json();
  },

  async getKYCStats(): Promise<{
    totalProcessed: number;
    averageTime: string;
    automationRate: number;
    pendingReview: number;
  }> {
    const response = await fetch(`${API_BASE_URL}/api/kyc/stats`);

    if (!response.ok) {
      throw new Error('Failed to get KYC stats');
    }

    return response.json();
  },

  async submitKYCDocuments(customerId: string, documents: File[]): Promise<{ workflowId: string; profile: KYCProfile }> {
    const formData = new FormData();
    formData.append('customerId', customerId);
    documents.forEach((doc, index) => {
      formData.append(`document_${index}`, doc);
    });

    const response = await fetch(`${API_BASE_URL}/api/kyc/documents`, {
      method: 'POST',
      body: formData,
    });

    if (!response.ok) {
      throw new Error('Failed to upload KYC documents');
    }

    return response.json();
  },

  // Regulatory methods
  async getRegulatoryUpdates(): Promise<RegulatoryUpdate[]> {
    const response = await fetch(`${API_BASE_URL}/api/regulatory/updates`);

    if (!response.ok) {
      throw new Error('Failed to get regulatory updates');
    }

    return response.json();
  },

  async getRegulatoryStats(): Promise<{
    totalUpdates: number;
    actionRequired: number;
    highImpact: number;
    recentUpdates: number;
  }> {
    const response = await fetch(`${API_BASE_URL}/api/regulatory/stats`);

    if (!response.ok) {
      throw new Error('Failed to get regulatory stats');
    }

    return response.json();
  },

  async getMonitoredSources(): Promise<Array<{
    name: string;
    status: 'active' | 'inactive';
    lastCheck: string;
    updatesCount: number;
  }>> {
    const response = await fetch(`${API_BASE_URL}/api/regulatory/sources`);

    if (!response.ok) {
      throw new Error('Failed to get monitored sources');
    }

    return response.json();
  },

  async analyzeRegulatoryImpact(data: {
    regulatoryUpdates: string[];
    timeframe: string;
  }): Promise<{ workflowId: string; analysis: { impactLevel: string; recommendations: string[]; affectedAreas: string[] } }> {
    const response = await fetch(`${API_BASE_URL}/api/regulatory/analyze-impact`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      throw new Error('Failed to analyze regulatory impact');
    }

    return response.json();
  },

  // Report methods
  async getRecentReports(): Promise<ComplianceReport[]> {
    const response = await fetch(`${API_BASE_URL}/api/reports/recent`);

    if (!response.ok) {
      throw new Error('Failed to get recent reports');
    }

    return response.json();
  },

  async getReportStats(): Promise<{
    totalReports: number;
    monthlyReports: number;
    quarterlyReports: number;
    annualReports: number;
    avgGenerationTime: string;
  }> {
    const response = await fetch(`${API_BASE_URL}/api/reports/stats`);

    if (!response.ok) {
      throw new Error('Failed to get report stats');
    }

    return response.json();
  },

  // Workflow methods
  async getActiveWorkflows(): Promise<WorkflowStatus[]> {
    const response = await fetch(`${API_BASE_URL}/api/workflows/active`);

    if (!response.ok) {
      throw new Error('Failed to get active workflows');
    }

    return response.json();
  },

  async getWorkflowStats(): Promise<{
    totalWorkflows: number;
    runningWorkflows: number;
    completedWorkflows: number;
    failedWorkflows: number;
    avgExecutionTime: string;
  }> {
    const response = await fetch(`${API_BASE_URL}/api/workflows/stats`);

    if (!response.ok) {
      throw new Error('Failed to get workflow stats');
    }

    return response.json();
  },

  async getWorkflowMetrics(): Promise<{
    throughput: string;
    successRate: string;
    avgLatency: string;
    errorRate: string;
  }> {
    const response = await fetch(`${API_BASE_URL}/api/workflows/metrics`);

    if (!response.ok) {
      throw new Error('Failed to get workflow metrics');
    }

    return response.json();
  }
};
